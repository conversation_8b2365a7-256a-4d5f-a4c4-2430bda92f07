# Trusty TEE GP标准可信存储功能概要设计文档

## 1. 引言

### 1.1 文档目的

本文档旨在定义Trusty TEE中GP标准可信存储功能的概要设计方案，基于OP-TEE成熟的双层对象模型架构，实现完全兼容GP标准的存储功能，同时充分利用Trusty TEE的性能优势和安全特性。

### 1.2 设计目标

**主要目标：**

- **完全GP标准兼容**：实现GP TEE Internal Core API v1.3.1规范中的所有存储相关API
- **OP-TEE架构适配**：基于OP-TEE成熟的双层对象模型（tee_obj + tee_pobj）进行精确适配
- **Trusty原生集成**：充分利用Trusty TEE的TIPC通信机制和内核服务架构
- **零修改兼容性**：确保现有TA应用无需修改即可使用GP存储功能

**性能目标：**

- 支持高并发存储操作（>100个并发对象）
- 大数据传输性能优化（>1MB/s传输速率）
- 内存使用效率优化（<10MB内存占用）
- 响应时间控制（<10ms平均响应时间）

### 1.3 设计原则

**架构设计原则：**

1. **OP-TEE兼容性优先**：完全保持OP-TEE的对象管理逻辑和并发控制机制
2. **最小化修改原则**：在现有Trusty架构基础上进行最小化扩展（<500行代码）
3. **模块化设计原则**：GP存储功能作为独立模块，可选择性编译和部署
4. **向后兼容原则**：不影响现有generic_ta_service功能和TA应用

**技术设计原则：**

1. **双层抽象设计**：句柄层（trusty_tee_obj）+ 持久对象层（trusty_pobj）分离关注点
2. **统一接口设计**：通过trusty_storage_ops提供统一的存储后端抽象
3. **安全隔离设计**：基于TA UUID的完全隔离机制
4. **性能优化设计**：TIPC消息优化、属性压缩传输、并发控制优化

### 1.4 参考标准和依赖

**标准规范：**

- GP TEE Internal Core API Specification v1.3.1
- GP TEE Client API Specification v1.0
- GP TEE System Architecture v1.2

**技术依赖：**

- Trusty TEE内核服务框架
- TIPC（Trusty IPC）通信机制
- generic_ta_service扩展框架
- OP-TEE存储架构设计参考

## 2. 概述

### 2.1 功能概述

Trusty TEE GP标准可信存储功能提供完整的GP标准存储API实现，支持瞬态对象和持久对象的创建、管理、数据操作和枚举功能。通过基于OP-TEE双层对象模型的精确适配设计，确保与GP标准的完全兼容性。

**核心功能模块：**

1. **瞬态对象管理**：

   - 对象分配、释放、重置
   - 属性填充和复制
   - 密钥生成和管理
2. **持久对象管理**：

   - 对象创建、打开、删除
   - 对象重命名和权限控制
   - 多存储空间支持
3. **数据流操作**：

   - 数据读取、写入、截断
   - 数据定位和流控制
   - 大数据分片传输
4. **对象枚举**：

   - 枚举器分配和管理
   - 对象列表遍历
   - 存储空间枚举
5. **对象信息管理**：

   - 对象信息查询
   - 属性获取和设置
   - 使用权限控制

### 2.2 架构概述

**基于OP-TEE的双层架构设计：**

```mermaid
graph TB
    subgraph "TA Application Layer"
        A1[GP Storage APIs]
        A2[TEE_CreatePersistentObject<br/>TEE_OpenPersistentObject<br/>TEE_AllocateTransientObject]
    end

    subgraph "User Space Adapter Layer"
        B1[libutee Storage Adapter]
        B2[TIPC Client Interface]
        B3[OP-TEE Compatible Message Format]
    end

    subgraph "Kernel Service Layer - OP-TEE Architecture Adaptation"
        C1[Generic TA Service Extension]
        C2[trusty_tee_obj Manager<br/>基于OP-TEE tee_obj]
        C3[trusty_pobj Manager<br/>基于OP-TEE tee_pobj]
        C4[trusty_storage_ops<br/>基于OP-TEE tee_file_operations]
    end

    subgraph "Storage Backend Layer"
        D1[Trusty Storage Service]
        D2[File System Interface]
    end

    A1 --> A2
    A2 --> B1
    B1 --> B2
    B2 --> B3
    B3 --> C1
    C1 --> C2
    C2 --> C3
    C3 --> C4
    C4 --> D1
    D1 --> D2
```

**OP-TEE双层对象模型适配：**

- **句柄层（trusty_tee_obj）**：对应OP-TEE的struct tee_obj，管理TA私有的对象句柄
- **持久对象层（trusty_pobj）**：对应OP-TEE的struct tee_pobj，管理全局共享的持久对象
- **存储抽象层（trusty_storage_ops）**：对应OP-TEE的tee_file_operations，提供统一存储接口

### 2.3 关键技术特性

**OP-TEE兼容性特性：**

1. **完整字段映射**：所有OP-TEE关键字段（busy、have_attrs、refcnt、creating等）完全保持
2. **一致并发控制**：busy标志 + 引用计数 + creating标志的OP-TEE并发策略完全复制
3. **相同状态管理**：对象生命周期和状态转换逻辑与OP-TEE完全一致
4. **统一错误处理**：错误码和异常处理机制与OP-TEE保持一致

**Trusty优化特性：**

1. **TIPC消息优化**：基于have_attrs位字段的属性压缩传输
2. **并发性能增强**：Trusty mutex + OP-TEE并发控制的双重保护
3. **内存管理优化**：对象池化和缓存机制
4. **安全隔离增强**：TA UUID隔离 + Trusty安全机制

### 2.4 系统边界和约束

**功能边界：**

- 支持GP标准定义的所有存储API（30+个函数）
- 支持3种存储类型：TEE_STORAGE_PRIVATE、TEE_STORAGE_PERSO、TEE_STORAGE_PROTECTED
- 支持对象类型：持久对象、瞬态对象、数据对象、密钥对象
- 支持并发访问：每个TA最多64个并发对象句柄

**性能约束：**

- 最大单个对象大小：16MB
- 最大并发TA数量：32个
- TIPC消息大小限制：64KB（支持分片传输）
- 内存使用限制：总计不超过32MB

**安全约束：**

- TA间完全隔离：不同TA无法访问彼此的存储对象
- 权限严格控制：基于对象创建时的访问标志进行权限检查
- 数据完整性保护：存储数据的完整性校验和防篡改机制
- 安全清除机制：敏感数据的安全清除和内存保护

## 3. 总体设计

### 3.1 基于OP-TEE的双层对象架构设计

**OP-TEE双层对象模型核心价值：**

OP-TEE的双层对象架构（struct tee_obj + struct tee_pobj）是经过多年实际应用验证的成熟设计，具有以下核心优势：

1. **关注点分离**：句柄管理与持久存储分离，降低系统复杂度
2. **多句柄支持**：通过引用计数支持同一持久对象的多个句柄访问
3. **并发控制成熟**：busy标志 + creating标志 + 引用计数的三重并发保护
4. **TA隔离完善**：基于UUID的严格TA隔离机制
5. **存储抽象灵活**：通过操作接口支持多种存储后端

**Trusty TEE双层架构适配设计：**

```mermaid
graph TB
    subgraph "TA Private Object Space"
        A1[trusty_tee_obj 1<br/>基于OP-TEE tee_obj]
        A2[trusty_tee_obj 2<br/>基于OP-TEE tee_obj]
        A3[trusty_tee_obj N<br/>基于OP-TEE tee_obj]
    end

    subgraph "Global Persistent Object Space"
        B1[trusty_pobj 1<br/>基于OP-TEE tee_pobj<br/>refcnt=2]
        B2[trusty_pobj 2<br/>基于OP-TEE tee_pobj<br/>refcnt=1]
    end

    subgraph "Storage Backend Abstraction"
        C1[trusty_storage_ops<br/>基于OP-TEE tee_file_operations]
        C2[Trusty Storage Service]
    end

    A1 --> B1
    A2 --> B1
    A3 --> B2
    B1 --> C1
    B2 --> C1
    C1 --> C2
```

**架构适配关键要点：**

1. **完整字段映射**：所有OP-TEE关键字段在Trusty结构中完全保持
2. **一致行为逻辑**：对象生命周期管理与OP-TEE完全一致
3. **兼容并发控制**：OP-TEE的并发控制机制完全复制
4. **统一错误处理**：错误码和异常处理与OP-TEE保持一致

### 3.2 核心数据结构设计

#### 3.2.1 对象句柄结构（基于OP-TEE tee_obj完全适配）

```c
/* Trusty对象句柄 - 完全基于OP-TEE tee_obj设计 */
struct trusty_tee_obj {
    /* 链表管理 - 对应OP-TEE的TAILQ_ENTRY */
    struct list_node link;         /* TA私有对象链表节点 */

    /* GP标准对象信息 - 完全兼容OP-TEE */
    TEE_ObjectInfo info;           /* GP标准对象信息 */

    /* 并发控制 - 完全保持OP-TEE设计 */
    bool busy;                     /* 操作忙标志，防止并发操作 */

    /* 属性管理 - 完全保持OP-TEE设计 */
    uint32_t have_attrs;           /* 属性位字段，标识已设置的属性 */
    void *attr;                    /* 属性数据指针 */

    /* 数据流管理 - 完全保持OP-TEE设计 */
    size_t ds_pos;                 /* 数据流起始位置偏移 */

    /* 存储抽象 - 适配Trusty机制 */
    struct trusty_pobj *pobj;      /* 指向持久化对象的指针 */
    struct handle *fh;             /* 文件句柄，对应OP-TEE的tee_file_handle */

    /* Trusty扩展字段（最小化） */
    uint32_t handle_id;            /* TIPC句柄ID */
    mutex_t obj_lock;              /* Trusty对象级锁（增强并发控制） */
};
```

**OP-TEE字段映射说明：**


| OP-TEE字段                   | Trusty字段                 | 适配说明                   |
| ---------------------------- | -------------------------- | -------------------------- |
| `TAILQ_ENTRY(tee_obj) link`  | `struct list_node link`    | 使用Trusty链表机制         |
| `TEE_ObjectInfo info`        | `TEE_ObjectInfo info`      | 完全保持GP标准兼容         |
| `bool busy`                  | `bool busy`                | 完全保持OP-TEE并发控制逻辑 |
| `uint32_t have_attrs`        | `uint32_t have_attrs`      | 完全保持属性位字段机制     |
| `void *attr`                 | `void *attr`               | 完全保持属性数据指针       |
| `size_t ds_pos`              | `size_t ds_pos`            | 完全保持数据流位置管理     |
| `struct tee_pobj *pobj`      | `struct trusty_pobj *pobj` | 适配Trusty持久对象         |
| `struct tee_file_handle *fh` | `struct handle *fh`        | 使用Trusty文件句柄         |

#### 3.2.2 持久对象结构（基于OP-TEE tee_pobj完全适配）

```c
/* Trusty持久对象 - 完全基于OP-TEE tee_pobj设计 */
struct trusty_pobj {
    /* 链表管理 - 对应OP-TEE的TAILQ_ENTRY */
    struct list_node link;        /* 全局持久化对象链表节点 */

    /* 引用计数 - 完全保持OP-TEE设计 */
    uint32_t refcnt;              /* 引用计数，支持多句柄访问同一对象 */

    /* TA隔离 - 完全保持OP-TEE设计 */
    struct uuid uuid;             /* 拥有该对象的TA的UUID，实现TA隔离 */

    /* 对象标识 - 完全保持OP-TEE设计 */
    void *obj_id;                 /* 对象标识符，由TA指定 */
    uint32_t obj_id_len;          /* 对象标识符长度 */

    /* 访问控制 - 完全保持OP-TEE设计 */
    uint32_t flags;               /* 访问标志（读/写/共享权限） */
    uint32_t obj_info_usage;      /* 对象使用权限（密钥用途等） */

    /* 状态管理 - 完全保持OP-TEE设计 */
    bool temporary;               /* 临时对象标志，创建过程中为true */
    bool creating;                /* 创建中标志，防止并发访问冲突 */

    /* 存储后端 - 适配Trusty机制 */
    const struct trusty_storage_ops *fops; /* 文件系统操作接口指针 */

    /* Trusty扩展字段（最小化） */
    char storage_path[STORAGE_MAX_PATH_LEN]; /* 存储文件路径 */
};
```

**OP-TEE字段映射说明：**


| OP-TEE字段                               | Trusty字段                              | 适配说明               |
| ---------------------------------------- | --------------------------------------- | ---------------------- |
| `TAILQ_ENTRY(tee_pobj) link`             | `struct list_node link`                 | 使用Trusty链表机制     |
| `uint32_t refcnt`                        | `uint32_t refcnt`                       | 完全保持引用计数机制   |
| `TEE_UUID uuid`                          | `struct uuid uuid`                      | 适配Trusty UUID类型    |
| `void *obj_id`                           | `void *obj_id`                          | 完全保持对象ID指针     |
| `uint32_t obj_id_len`                    | `uint32_t obj_id_len`                   | 完全保持ID长度         |
| `uint32_t flags`                         | `uint32_t flags`                        | 完全保持访问标志       |
| `uint32_t obj_info_usage`                | `uint32_t obj_info_usage`               | 完全保持使用权限       |
| `bool temporary`                         | `bool temporary`                        | 完全保持临时对象标志   |
| `bool creating`                          | `bool creating`                         | 完全保持创建中标志     |
| `const struct tee_file_operations *fops` | `const struct trusty_storage_ops *fops` | 适配Trusty存储操作接口 |

### 3.3 存储操作接口设计

#### 3.3.1 存储操作接口结构（基于OP-TEE tee_file_operations适配）

```c
/* Trusty存储操作接口 - 完全基于OP-TEE tee_file_operations设计 */
struct trusty_storage_ops {
    /* 基础文件操作 - 对应OP-TEE接口 */
    TEE_Result (*open)(struct trusty_pobj *pobj, size_t *size,
                      struct handle **fh);
    TEE_Result (*create)(struct trusty_pobj *pobj, bool overwrite,
                        const void *head, size_t head_size,
                        const void *attr, size_t attr_size,
                        struct handle **fh);
    TEE_Result (*close)(struct handle **fh);
    TEE_Result (*read)(struct handle *fh, size_t pos,
                      void *buf, size_t *len);
    TEE_Result (*write)(struct handle *fh, size_t pos,
                       const void *buf, size_t len);
    TEE_Result (*truncate)(struct handle *fh, size_t len);
    TEE_Result (*remove)(struct trusty_pobj *pobj);
    TEE_Result (*rename)(struct trusty_pobj *old_pobj,
                        struct trusty_pobj *new_pobj,
                        bool overwrite);

    /* 枚举操作 - 对应OP-TEE目录操作 */
    TEE_Result (*opendir)(const struct uuid *uuid,
                         struct handle **d);
    TEE_Result (*readdir)(struct handle *d,
                         struct trusty_pobj **pobj);
    TEE_Result (*closedir)(struct handle *d);
};
```

**OP-TEE接口映射说明：**

存储操作接口完全对应OP-TEE的`tee_file_operations`，提供以下核心功能：

1. **基础文件操作**：open、create、close、read、write、truncate、remove、rename
2. **枚举操作**：opendir、readdir、closedir
3. **OP-TEE兼容性**：接口签名和语义与OP-TEE完全一致
4. **Trusty适配**：使用Trusty的handle机制和错误处理

### 3.4 OP-TEE并发控制机制适配

#### 3.4.1 基于OP-TEE的并发控制策略

**OP-TEE三重并发保护机制：**

1. **busy标志保护**：防止对象句柄的并发操作
2. **creating标志保护**：防止持久对象创建过程中的并发访问
3. **引用计数保护**：管理持久对象的多句柄访问

**Trusty TEE并发控制适配实现：**

```c
/* 对象忙状态管理 - 完全基于OP-TEE逻辑 */
TEE_Result trusty_obj_set_busy(struct trusty_tee_obj *obj) {
    mutex_acquire(&obj->obj_lock);

    if (obj->busy) {
        mutex_release(&obj->obj_lock);
        return TEE_ERROR_BUSY;
    }

    obj->busy = true;
    mutex_release(&obj->obj_lock);
    return TEE_SUCCESS;
}

void trusty_obj_clear_busy(struct trusty_tee_obj *obj) {
    mutex_acquire(&obj->obj_lock);
    obj->busy = false;
    mutex_release(&obj->obj_lock);
}

/* 持久对象引用计数管理 - 完全基于OP-TEE逻辑 */
void trusty_pobj_get(struct trusty_pobj *pobj) {
    mutex_acquire(&global_pobj_manager.global_lock);
    pobj->refcnt++;
    mutex_release(&global_pobj_manager.global_lock);
}

void trusty_pobj_put(struct trusty_pobj *pobj) {
    bool should_free = false;

    mutex_acquire(&global_pobj_manager.global_lock);
    assert(pobj->refcnt > 0);
    pobj->refcnt--;

    if (pobj->refcnt == 0) {
        list_delete(&pobj->link);
        global_pobj_manager.pobj_count--;
        should_free = true;
    }
    mutex_release(&global_pobj_manager.global_lock);

    if (should_free) {
        free(pobj->obj_id);
        free(pobj);
    }
}
```

#### 3.4.2 对象生命周期管理（基于OP-TEE逻辑）

**瞬态对象分配流程：**

```c
/* 完全基于OP-TEE tee_obj_alloc逻辑 */
TEE_Result trusty_obj_alloc(uint32_t obj_type, uint32_t max_obj_size,
                           struct trusty_tee_obj **obj) {
    struct trusty_tee_obj *o;

    /* 分配对象结构 */
    o = calloc(1, sizeof(*o));
    if (!o)
        return TEE_ERROR_OUT_OF_MEMORY;

    /* 初始化基础字段 - 保持OP-TEE逻辑 */
    o->info.objectType = obj_type;
    o->info.maxObjectSize = max_obj_size;
    o->info.objectSize = 0;
    o->info.objectUsage = 0xFFFFFFFF;  /* 初始允许所有用法 */
    o->busy = false;                   /* 初始非忙状态 */
    o->have_attrs = 0;                 /* 初始无属性 */
    o->attr = NULL;
    o->ds_pos = 0;                     /* 数据流位置初始化 */
    o->pobj = NULL;                    /* 瞬态对象无持久对象 */
    o->fh = NULL;                      /* 瞬态对象无文件句柄 */

    /* Trusty特有初始化 */
    o->handle_id = generate_handle_id();
    mutex_init(&o->obj_lock);

    *obj = o;
    return TEE_SUCCESS;
}
```

### 3.5 TIPC通信机制设计

#### 3.5.1 基于OP-TEE的消息格式设计

**TIPC消息头结构：**

```c
struct gp_storage_msg_hdr {
    struct generic_ta_msg_hdr base;    // 复用现有消息头
    uint32_t object_handle;            // 对象句柄ID
    uint32_t storage_id;               // 存储空间ID (TEE_STORAGE_*)
    uint32_t operation_flags;          // 操作标志位
    uint32_t sequence_number;          // 消息序列号
    uint32_t total_size;               // 总数据大小
    uint32_t fragment_offset;          // 分片偏移量
    uint32_t fragment_size;            // 当前分片大小
    uint32_t reserved[2];              // 预留扩展字段
};
```

**命令枚举定义：**

```c
enum gp_storage_cmd {
    /* 瞬态对象操作命令 (50-59) */
    GP_STORAGE_ALLOC_TRANSIENT = 50,
    GP_STORAGE_FREE_TRANSIENT = 51,
    GP_STORAGE_RESET_TRANSIENT = 52,
    GP_STORAGE_POPULATE_TRANSIENT = 53,
    GP_STORAGE_COPY_ATTRIBUTES = 54,
    GP_STORAGE_GENERATE_KEY = 55,

    /* 持久对象操作命令 (60-69) */
    GP_STORAGE_OPEN_PERSISTENT = 60,
    GP_STORAGE_CREATE_PERSISTENT = 61,
    GP_STORAGE_CLOSE_DELETE_PERSISTENT = 62,
    GP_STORAGE_RENAME_PERSISTENT = 63,

    /* 对象信息操作命令 (70-79) */
    GP_STORAGE_GET_OBJECT_INFO = 70,
    GP_STORAGE_RESTRICT_USAGE = 71,
    GP_STORAGE_GET_BUFFER_ATTR = 72,
    GP_STORAGE_GET_VALUE_ATTR = 73,
    GP_STORAGE_CLOSE_OBJECT = 74,

    /* 数据流操作命令 (80-89) */
    GP_STORAGE_READ_DATA = 80,
    GP_STORAGE_WRITE_DATA = 81,
    GP_STORAGE_TRUNCATE_DATA = 82,
    GP_STORAGE_SEEK_DATA = 83,

    /* 对象枚举命令 (90-99) */
    GP_STORAGE_ALLOC_ENUMERATOR = 90,
    GP_STORAGE_FREE_ENUMERATOR = 91,
    GP_STORAGE_START_ENUM = 92,
    GP_STORAGE_GET_NEXT_OBJECT = 93,
};
```

#### 3.5.2 基于have_attrs的属性优化传输

**属性传输优化机制：**

```c
/* 基于OP-TEE have_attrs位字段的优化传输 */
struct trusty_attr_transfer {
    uint32_t have_attrs;           /* 属性位字段 */
    uint32_t attr_count;           /* 实际属性数量 */
    size_t total_size;             /* 总数据大小 */
    /* 后跟压缩的属性数据 */
};

/* 属性压缩传输接口 */
TEE_Result trusty_attr_pack(const TEE_Attribute *attrs,
                           uint32_t attr_count,
                           struct trusty_attr_transfer **packed,
                           size_t *packed_size);

TEE_Result trusty_attr_unpack(const struct trusty_attr_transfer *packed,
                             TEE_Attribute **attrs,
                             uint32_t *attr_count);
```

### 3.6 基于架构图的完整TIPC接口映射设计

#### 3.6.1 五层接口映射架构

**完整的GP存储API到TIPC接口映射：**

```mermaid
graph TB
    subgraph "GP Storage API Layer"
        A1[Transient Object APIs<br/>TEE_AllocateTransientObject<br/>TEE_FreeTransientObject<br/>TEE_ResetTransientObject<br/>TEE_PopulateTransientObject<br/>TEE_CopyObjectAttributes1<br/>TEE_GenerateKey]
        A2[Persistent Object APIs<br/>TEE_OpenPersistentObject<br/>TEE_CreatePersistentObject<br/>TEE_CloseAndDeletePersistentObject1<br/>TEE_RenamePersistentObject]
        A3[Object Info/Attribute APIs<br/>TEE_GetObjectInfo1<br/>TEE_RestrictObjectUsage1<br/>TEE_GetObjectBufferAttribute<br/>TEE_GetObjectValueAttribute<br/>TEE_CloseObject]
        A4[Data Stream APIs<br/>TEE_ReadObjectData<br/>TEE_WriteObjectData<br/>TEE_TruncateObjectData<br/>TEE_SeekObjectData]
        A5[Object Enumeration APIs<br/>TEE_AllocatePersistentObjectEnumerator<br/>TEE_FreePersistentObjectEnumerator<br/>TEE_ResetPersistentObjectEnumerator<br/>TEE_StartPersistentObjectEnumerator<br/>TEE_GetNextPersistentObject]
    end

    subgraph "UTEE Syscalls Layer"
        B1[Transient UTEE Syscalls<br/>_utee_cryp_obj_alloc<br/>_utee_cryp_obj_close<br/>_utee_cryp_obj_reset<br/>_utee_cryp_obj_populate<br/>_utee_cryp_obj_copy<br/>_utee_cryp_obj_generate_key]
        B2[Persistent UTEE Syscalls<br/>_utee_storage_obj_open<br/>_utee_storage_obj_create<br/>_utee_storage_obj_del<br/>_utee_storage_obj_rename]
        B3[Info/Attr UTEE Syscalls<br/>_utee_cryp_obj_get_info<br/>_utee_cryp_obj_restrict_usage<br/>_utee_cryp_obj_get_attr<br/>_utee_cryp_obj_close]
        B4[DataStream UTEE Syscalls<br/>_utee_storage_obj_read<br/>_utee_storage_obj_write<br/>_utee_storage_obj_trunc<br/>_utee_storage_obj_seek]
        B5[Enum UTEE Syscalls<br/>_utee_storage_alloc_enum<br/>_utee_storage_free_enum<br/>_utee_storage_reset_enum<br/>_utee_storage_start_enum<br/>_utee_storage_next_enum]
    end

    subgraph "TIPC Interface Layer"
        C1[Transient TIPC Handlers<br/>handle_alloc_transient<br/>handle_free_transient<br/>handle_reset_transient<br/>handle_populate_transient<br/>handle_copy_attributes<br/>handle_generate_key]
        C2[Persistent TIPC Handlers<br/>handle_open_persistent<br/>handle_create_persistent<br/>handle_close_delete_persistent<br/>handle_rename_persistent]
        C3[Info/Attr TIPC Handlers<br/>handle_get_object_info<br/>handle_restrict_usage<br/>handle_get_buffer_attr<br/>handle_get_value_attr<br/>handle_close_object]
        C4[DataStream TIPC Handlers<br/>handle_read_data<br/>handle_write_data<br/>handle_truncate_data<br/>handle_seek_data]
        C5[Enum TIPC Handlers<br/>handle_alloc_enumerator<br/>handle_free_enumerator<br/>handle_reset_enumerator<br/>handle_start_enum<br/>handle_get_next_object]
    end

    subgraph "Kernel Syscalls Layer"
        D1[Transient Kernel Syscalls<br/>syscall_cryp_obj_alloc<br/>syscall_cryp_obj_close<br/>syscall_cryp_obj_reset<br/>syscall_cryp_obj_populate<br/>syscall_cryp_obj_copy<br/>syscall_cryp_obj_generate_key]
        D2[Persistent Kernel Syscalls<br/>syscall_storage_obj_open<br/>syscall_storage_obj_create<br/>syscall_storage_obj_del<br/>syscall_storage_obj_rename]
        D3[Info/Attr Kernel Syscalls<br/>syscall_cryp_obj_get_info<br/>syscall_cryp_obj_restrict_usage<br/>syscall_cryp_obj_get_attr<br/>syscall_cryp_obj_close]
        D4[DataStream Kernel Syscalls<br/>syscall_storage_obj_read<br/>syscall_storage_obj_write<br/>syscall_storage_obj_trunc<br/>syscall_storage_obj_seek]
        D5[Enum Kernel Syscalls<br/>syscall_storage_alloc_enum<br/>syscall_storage_free_enum<br/>syscall_storage_reset_enum<br/>syscall_storage_start_enum<br/>syscall_storage_next_enum]
    end

    subgraph "Object Management Layer"
        E1[Transient Object Management<br/>tee_obj_alloc<br/>tee_obj_free<br/>tee_obj_attr_clear<br/>tee_obj_attr_copy_from<br/>tee_obj_attr_copy<br/>tee_obj_generate_key]
        E2[Persistent Object Management<br/>tee_svc_storage_init_file<br/>tee_obj_add<br/>tee_obj_close<br/>tee_pobj_get<br/>tee_pobj_create_final<br/>tee_pobj_release]
        E3[Info/Attr Object Management<br/>tee_obj_get_info<br/>tee_obj_restrict_usage<br/>tee_obj_get_attr<br/>tee_obj_get]
        E4[DataStream Object Management<br/>tee_obj_get<br/>position_calculation<br/>access_control_check<br/>data_size_update]
        E5[Enum Object Management<br/>tee_obj_enum_alloc<br/>tee_obj_enum_free<br/>tee_obj_enum_reset<br/>tee_obj_enum_start<br/>tee_obj_enum_next]
    end

    %% 调用关系映射
    A1 --> B1 --> C1 --> D1 --> E1
    A2 --> B2 --> C2 --> D2 --> E2
    A3 --> B3 --> C3 --> D3 --> E3
    A4 --> B4 --> C4 --> D4 --> E4
    A5 --> B5 --> C5 --> D5 --> E5
```

#### 3.6.2 generic_ta_service扩展策略

**完整的TIPC命令处理映射：**

```c
/* generic_ta_service扩展处理 - 完整接口映射 */
static int handle_gp_storage_message(struct generic_ta_channel *chan,
                                   struct generic_ta_msg_hdr *hdr,
                                   void *msg_payload, size_t payload_size) {
    struct gp_storage_msg_hdr *storage_hdr = (struct gp_storage_msg_hdr *)hdr;

    switch (hdr->cmd) {
    /* 瞬态对象操作命令 (50-59) */
    case GP_STORAGE_ALLOC_TRANSIENT:
        return handle_alloc_transient(chan, storage_hdr, msg_payload);
    case GP_STORAGE_FREE_TRANSIENT:
        return handle_free_transient(chan, storage_hdr, msg_payload);
    case GP_STORAGE_RESET_TRANSIENT:
        return handle_reset_transient(chan, storage_hdr, msg_payload);
    case GP_STORAGE_POPULATE_TRANSIENT:
        return handle_populate_transient(chan, storage_hdr, msg_payload);
    case GP_STORAGE_COPY_ATTRIBUTES:
        return handle_copy_attributes(chan, storage_hdr, msg_payload);
    case GP_STORAGE_GENERATE_KEY:
        return handle_generate_key(chan, storage_hdr, msg_payload);

    /* 持久对象操作命令 (60-69) */
    case GP_STORAGE_OPEN_PERSISTENT:
        return handle_open_persistent(chan, storage_hdr, msg_payload);
    case GP_STORAGE_CREATE_PERSISTENT:
        return handle_create_persistent(chan, storage_hdr, msg_payload);
    case GP_STORAGE_CLOSE_DELETE_PERSISTENT:
        return handle_close_delete_persistent(chan, storage_hdr, msg_payload);
    case GP_STORAGE_RENAME_PERSISTENT:
        return handle_rename_persistent(chan, storage_hdr, msg_payload);

    /* 对象信息操作命令 (70-79) */
    case GP_STORAGE_GET_OBJECT_INFO:
        return handle_get_object_info(chan, storage_hdr, msg_payload);
    case GP_STORAGE_RESTRICT_USAGE:
        return handle_restrict_usage(chan, storage_hdr, msg_payload);
    case GP_STORAGE_GET_BUFFER_ATTR:
        return handle_get_buffer_attr(chan, storage_hdr, msg_payload);
    case GP_STORAGE_GET_VALUE_ATTR:
        return handle_get_value_attr(chan, storage_hdr, msg_payload);
    case GP_STORAGE_CLOSE_OBJECT:
        return handle_close_object(chan, storage_hdr, msg_payload);

    /* 数据流操作命令 (80-89) */
    case GP_STORAGE_READ_DATA:
        return handle_read_data(chan, storage_hdr, msg_payload);
    case GP_STORAGE_WRITE_DATA:
        return handle_write_data(chan, storage_hdr, msg_payload);
    case GP_STORAGE_TRUNCATE_DATA:
        return handle_truncate_data(chan, storage_hdr, msg_payload);
    case GP_STORAGE_SEEK_DATA:
        return handle_seek_data(chan, storage_hdr, msg_payload);

    /* 对象枚举命令 (90-99) */
    case GP_STORAGE_ALLOC_ENUMERATOR:
        return handle_alloc_enumerator(chan, storage_hdr, msg_payload);
    case GP_STORAGE_FREE_ENUMERATOR:
        return handle_free_enumerator(chan, storage_hdr, msg_payload);
    case GP_STORAGE_START_ENUM:
        return handle_start_enum(chan, storage_hdr, msg_payload);
    case GP_STORAGE_GET_NEXT_OBJECT:
        return handle_get_next_object(chan, storage_hdr, msg_payload);

    default:
        return ERR_NOT_SUPPORTED;
    }
}
```

#### 3.6.3 存储上下文管理

**TA存储上下文结构：**

```c
struct trusty_storage_context {
    struct uuid ta_uuid;               // TA唯一标识符
    list_t transient_objects;          // 瞬态对象链表
    list_t persistent_handles;         // 持久对象句柄链表
    list_t enumerators;                // 枚举器链表

    uint32_t next_handle_id;           // 下一个句柄ID
    uint32_t object_count;             // 当前对象数量
    uint32_t handle_count;             // 当前句柄数量
    uint32_t max_objects;              // 最大对象数限制

    mutex_t context_lock;              // 上下文级锁
    timestamp_t created_time;          // 上下文创建时间
    timestamp_t last_activity_time;    // 最后活动时间
};
```

## 4. 接口规范设计

### 4.1 用户接口规范（GP存储API）

#### 4.1.1 瞬态对象操作接口


| API函数                       | 功能说明             | 主要参数                  | 返回值     | OP-TEE对应函数           |
| ----------------------------- | -------------------- | ------------------------- | ---------- | ------------------------ |
| `TEE_AllocateTransientObject` | 分配未初始化瞬态对象 | objectType, maxObjectSize | TEE_Result | `tee_obj_alloc`          |
| `TEE_FreeTransientObject`     | 释放瞬态对象资源     | object                    | void       | `tee_obj_free`           |
| `TEE_ResetTransientObject`    | 重置瞬态对象状态     | object                    | void       | `tee_obj_attr_clear`     |
| `TEE_PopulateTransientObject` | 用属性填充瞬态对象   | object, attrs, attrCount  | TEE_Result | `tee_obj_attr_copy_from` |
| `TEE_CopyObjectAttributes1`   | 复制对象属性         | destObject, srcObject     | TEE_Result | `tee_obj_attr_copy`      |
| `TEE_GenerateKey`             | 生成随机密钥         | object, keySize, params   | TEE_Result | `tee_obj_generate_key`   |

#### 4.1.2 持久对象操作接口


| API函数                               | 功能说明         | 主要参数                               | 返回值     | OP-TEE对应函数               |
| ------------------------------------- | ---------------- | -------------------------------------- | ---------- | ---------------------------- |
| `TEE_OpenPersistentObject`            | 打开现有持久对象 | storageID, objectID, flags             | TEE_Result | `tee_svc_storage_obj_open`   |
| `TEE_CreatePersistentObject`          | 创建新持久对象   | storageID, objectID, flags, attributes | TEE_Result | `tee_svc_storage_obj_create` |
| `TEE_CloseAndDeletePersistentObject1` | 删除持久对象     | object                                 | TEE_Result | `tee_svc_storage_obj_del`    |
| `TEE_RenamePersistentObject`          | 重命名持久对象   | object, newObjectID                    | TEE_Result | `tee_svc_storage_obj_rename` |

#### 4.1.3 对象信息和属性接口


| API函数                        | 功能说明         | 主要参数                    | 返回值     | OP-TEE对应函数           |
| ------------------------------ | ---------------- | --------------------------- | ---------- | ------------------------ |
| `TEE_GetObjectInfo1`           | 获取对象信息     | object, objectInfo          | TEE_Result | `tee_obj_get_info`       |
| `TEE_RestrictObjectUsage1`     | 限制对象使用权限 | object, objectUsage         | TEE_Result | `tee_obj_restrict_usage` |
| `TEE_GetObjectBufferAttribute` | 获取缓冲区属性   | object, attributeID, buffer | TEE_Result | `tee_obj_get_attr`       |
| `TEE_GetObjectValueAttribute`  | 获取值属性       | object, attributeID, a, b   | TEE_Result | `tee_obj_get_attr`       |
| `TEE_CloseObject`              | 关闭对象句柄     | object                      | void       | `tee_obj_close`          |

#### 4.1.4 数据流操作接口


| API函数                  | 功能说明       | 主要参数                    | 返回值     | OP-TEE对应函数              |
| ------------------------ | -------------- | --------------------------- | ---------- | --------------------------- |
| `TEE_ReadObjectData`     | 从对象读取数据 | object, buffer, size, count | TEE_Result | `tee_svc_storage_obj_read`  |
| `TEE_WriteObjectData`    | 向对象写入数据 | object, buffer, size        | TEE_Result | `tee_svc_storage_obj_write` |
| `TEE_TruncateObjectData` | 截断对象数据   | object, size                | TEE_Result | `tee_svc_storage_obj_trunc` |
| `TEE_SeekObjectData`     | 设置数据位置   | object, offset, whence      | TEE_Result | `tee_svc_storage_obj_seek`  |

#### 4.1.5 对象枚举接口


| API函数                                  | 功能说明       | 主要参数                               | 返回值     | OP-TEE对应函数       |
| ---------------------------------------- | -------------- | -------------------------------------- | ---------- | -------------------- |
| `TEE_AllocatePersistentObjectEnumerator` | 分配对象枚举器 | objectEnumerator                       | TEE_Result | `tee_obj_enum_alloc` |
| `TEE_FreePersistentObjectEnumerator`     | 释放对象枚举器 | objectEnumerator                       | void       | `tee_obj_enum_free`  |
| `TEE_ResetPersistentObjectEnumerator`    | 重置枚举器状态 | objectEnumerator                       | void       | `tee_obj_enum_reset` |
| `TEE_StartPersistentObjectEnumerator`    | 开始对象枚举   | objectEnumerator, storageID            | TEE_Result | `tee_obj_enum_start` |
| `TEE_GetNextPersistentObject`            | 获取下一个对象 | objectEnumerator, objectInfo, objectID | TEE_Result | `tee_obj_enum_next`  |

### 4.2 TIPC接口规范（基于架构图的完整映射）

#### 4.2.1 瞬态对象操作TIPC接口规范


| TIPC接口函数                                                                                                           | 对应GP API                  | 功能描述                 | 参数说明                                                       | 返回值         | 空间标注 |
| ---------------------------------------------------------------------------------------------------------------------- | --------------------------- | ------------------------ | -------------------------------------------------------------- | -------------- | -------- |
| `static int handle_alloc_transient(struct generic_ta_channel *chan, struct gp_storage_msg_hdr *hdr, void *payload)`    | TEE_AllocateTransientObject | 通过TIPC分配瞬态对象     | chan: TIPC通道<br/>hdr: 消息头<br/>payload: 对象类型和大小     | ERR_NONE/ERR_* | 内核空间 |
| `static int handle_free_transient(struct generic_ta_channel *chan, struct gp_storage_msg_hdr *hdr, void *payload)`     | TEE_FreeTransientObject     | 通过TIPC释放瞬态对象     | chan: TIPC通道<br/>hdr: 消息头<br/>payload: 对象句柄           | ERR_NONE/ERR_* | 内核空间 |
| `static int handle_reset_transient(struct generic_ta_channel *chan, struct gp_storage_msg_hdr *hdr, void *payload)`    | TEE_ResetTransientObject    | 通过TIPC重置瞬态对象     | chan: TIPC通道<br/>hdr: 消息头<br/>payload: 对象句柄           | ERR_NONE/ERR_* | 内核空间 |
| `static int handle_populate_transient(struct generic_ta_channel *chan, struct gp_storage_msg_hdr *hdr, void *payload)` | TEE_PopulateTransientObject | 通过TIPC填充瞬态对象属性 | chan: TIPC通道<br/>hdr: 消息头<br/>payload: 对象句柄和属性数据 | ERR_NONE/ERR_* | 内核空间 |
| `static int handle_copy_attributes(struct generic_ta_channel *chan, struct gp_storage_msg_hdr *hdr, void *payload)`    | TEE_CopyObjectAttributes1   | 通过TIPC复制对象属性     | chan: TIPC通道<br/>hdr: 消息头<br/>payload: 源和目标对象句柄   | ERR_NONE/ERR_* | 内核空间 |
| `static int handle_generate_key(struct generic_ta_channel *chan, struct gp_storage_msg_hdr *hdr, void *payload)`       | TEE_GenerateKey             | 通过TIPC生成密钥         | chan: TIPC通道<br/>hdr: 消息头<br/>payload: 对象句柄和密钥参数 | ERR_NONE/ERR_* | 内核空间 |

#### 4.2.2 持久对象操作TIPC接口规范


| TIPC接口函数                                                                                                                | 对应GP API                          | 功能描述               | 参数说明                                                                   | 返回值         | 空间标注 |
| --------------------------------------------------------------------------------------------------------------------------- | ----------------------------------- | ---------------------- | -------------------------------------------------------------------------- | -------------- | -------- |
| `static int handle_open_persistent(struct generic_ta_channel *chan, struct gp_storage_msg_hdr *hdr, void *payload)`         | TEE_OpenPersistentObject            | 通过TIPC打开持久对象   | chan: TIPC通道<br/>hdr: 消息头<br/>payload: 存储ID、对象ID、访问标志       | ERR_NONE/ERR_* | 内核空间 |
| `static int handle_create_persistent(struct generic_ta_channel *chan, struct gp_storage_msg_hdr *hdr, void *payload)`       | TEE_CreatePersistentObject          | 通过TIPC创建持久对象   | chan: TIPC通道<br/>hdr: 消息头<br/>payload: 存储ID、对象ID、属性、初始数据 | ERR_NONE/ERR_* | 内核空间 |
| `static int handle_close_delete_persistent(struct generic_ta_channel *chan, struct gp_storage_msg_hdr *hdr, void *payload)` | TEE_CloseAndDeletePersistentObject1 | 通过TIPC删除持久对象   | chan: TIPC通道<br/>hdr: 消息头<br/>payload: 对象句柄                       | ERR_NONE/ERR_* | 内核空间 |
| `static int handle_rename_persistent(struct generic_ta_channel *chan, struct gp_storage_msg_hdr *hdr, void *payload)`       | TEE_RenamePersistentObject          | 通过TIPC重命名持久对象 | chan: TIPC通道<br/>hdr: 消息头<br/>payload: 对象句柄和新对象ID             | ERR_NONE/ERR_* | 内核空间 |

#### 4.2.3 对象信息和属性TIPC接口规范


| TIPC接口函数                                                                                                        | 对应GP API                   | 功能描述                 | 参数说明                                                       | 返回值         | 空间标注 |
| ------------------------------------------------------------------------------------------------------------------- | ---------------------------- | ------------------------ | -------------------------------------------------------------- | -------------- | -------- |
| `static int handle_get_object_info(struct generic_ta_channel *chan, struct gp_storage_msg_hdr *hdr, void *payload)` | TEE_GetObjectInfo1           | 通过TIPC获取对象信息     | chan: TIPC通道<br/>hdr: 消息头<br/>payload: 对象句柄           | ERR_NONE/ERR_* | 内核空间 |
| `static int handle_restrict_usage(struct generic_ta_channel *chan, struct gp_storage_msg_hdr *hdr, void *payload)`  | TEE_RestrictObjectUsage1     | 通过TIPC限制对象使用权限 | chan: TIPC通道<br/>hdr: 消息头<br/>payload: 对象句柄和使用权限 | ERR_NONE/ERR_* | 内核空间 |
| `static int handle_get_buffer_attr(struct generic_ta_channel *chan, struct gp_storage_msg_hdr *hdr, void *payload)` | TEE_GetObjectBufferAttribute | 通过TIPC获取缓冲区属性   | chan: TIPC通道<br/>hdr: 消息头<br/>payload: 对象句柄和属性ID   | ERR_NONE/ERR_* | 内核空间 |
| `static int handle_get_value_attr(struct generic_ta_channel *chan, struct gp_storage_msg_hdr *hdr, void *payload)`  | TEE_GetObjectValueAttribute  | 通过TIPC获取值属性       | chan: TIPC通道<br/>hdr: 消息头<br/>payload: 对象句柄和属性ID   | ERR_NONE/ERR_* | 内核空间 |
| `static int handle_close_object(struct generic_ta_channel *chan, struct gp_storage_msg_hdr *hdr, void *payload)`    | TEE_CloseObject              | 通过TIPC关闭对象句柄     | chan: TIPC通道<br/>hdr: 消息头<br/>payload: 对象句柄           | ERR_NONE/ERR_* | 内核空间 |

#### 4.2.4 数据流操作TIPC接口规范


| TIPC接口函数                                                                                                      | 对应GP API             | 功能描述             | 参数说明                                                             | 返回值         | 空间标注 |
| ----------------------------------------------------------------------------------------------------------------- | ---------------------- | -------------------- | -------------------------------------------------------------------- | -------------- | -------- |
| `static int handle_read_data(struct generic_ta_channel *chan, struct gp_storage_msg_hdr *hdr, void *payload)`     | TEE_ReadObjectData     | 通过TIPC读取对象数据 | chan: TIPC通道<br/>hdr: 消息头<br/>payload: 对象句柄、读取大小和偏移 | ERR_NONE/ERR_* | 内核空间 |
| `static int handle_write_data(struct generic_ta_channel *chan, struct gp_storage_msg_hdr *hdr, void *payload)`    | TEE_WriteObjectData    | 通过TIPC写入对象数据 | chan: TIPC通道<br/>hdr: 消息头<br/>payload: 对象句柄、写入数据       | ERR_NONE/ERR_* | 内核空间 |
| `static int handle_truncate_data(struct generic_ta_channel *chan, struct gp_storage_msg_hdr *hdr, void *payload)` | TEE_TruncateObjectData | 通过TIPC截断对象数据 | chan: TIPC通道<br/>hdr: 消息头<br/>payload: 对象句柄和新大小         | ERR_NONE/ERR_* | 内核空间 |
| `static int handle_seek_data(struct generic_ta_channel *chan, struct gp_storage_msg_hdr *hdr, void *payload)`     | TEE_SeekObjectData     | 通过TIPC设置数据位置 | chan: TIPC通道<br/>hdr: 消息头<br/>payload: 对象句柄、偏移和定位方式 | ERR_NONE/ERR_* | 内核空间 |

#### 4.2.5 对象枚举TIPC接口规范


| TIPC接口函数                                                                                                         | 对应GP API                             | 功能描述               | 参数说明                                                       | 返回值         | 空间标注 |
| -------------------------------------------------------------------------------------------------------------------- | -------------------------------------- | ---------------------- | -------------------------------------------------------------- | -------------- | -------- |
| `static int handle_alloc_enumerator(struct generic_ta_channel *chan, struct gp_storage_msg_hdr *hdr, void *payload)` | TEE_AllocatePersistentObjectEnumerator | 通过TIPC分配对象枚举器 | chan: TIPC通道<br/>hdr: 消息头<br/>payload: 枚举器参数         | ERR_NONE/ERR_* | 内核空间 |
| `static int handle_free_enumerator(struct generic_ta_channel *chan, struct gp_storage_msg_hdr *hdr, void *payload)`  | TEE_FreePersistentObjectEnumerator     | 通过TIPC释放对象枚举器 | chan: TIPC通道<br/>hdr: 消息头<br/>payload: 枚举器句柄         | ERR_NONE/ERR_* | 内核空间 |
| `static int handle_reset_enumerator(struct generic_ta_channel *chan, struct gp_storage_msg_hdr *hdr, void *payload)` | TEE_ResetPersistentObjectEnumerator    | 通过TIPC重置枚举器状态 | chan: TIPC通道<br/>hdr: 消息头<br/>payload: 枚举器句柄         | ERR_NONE/ERR_* | 内核空间 |
| `static int handle_start_enum(struct generic_ta_channel *chan, struct gp_storage_msg_hdr *hdr, void *payload)`       | TEE_StartPersistentObjectEnumerator    | 通过TIPC开始对象枚举   | chan: TIPC通道<br/>hdr: 消息头<br/>payload: 枚举器句柄和存储ID | ERR_NONE/ERR_* | 内核空间 |
| `static int handle_get_next_object(struct generic_ta_channel *chan, struct gp_storage_msg_hdr *hdr, void *payload)`  | TEE_GetNextPersistentObject            | 通过TIPC获取下一个对象 | chan: TIPC通道<br/>hdr: 消息头<br/>payload: 枚举器句柄         | ERR_NONE/ERR_* | 内核空间 |

### 4.3 内部辅助函数接口规范（基于OP-TEE适配）

#### 4.3.1 对象管理辅助函数


| 函数签名                                                                                                    | 功能描述         | 参数说明                                                                  | 返回值                              | 空间标注 |
| ----------------------------------------------------------------------------------------------------------- | ---------------- | ------------------------------------------------------------------------- | ----------------------------------- | -------- |
| `static TEE_Result trusty_obj_alloc(uint32_t obj_type, uint32_t max_obj_size, struct trusty_tee_obj **obj)` | 分配瞬态对象句柄 | obj_type: 对象类型<br/>max_obj_size: 最大对象大小<br/>obj: 返回的对象指针 | TEE_SUCCESS/TEE_ERROR_OUT_OF_MEMORY | 内核空间 |
| `static void trusty_obj_free(struct trusty_tee_obj *obj)`                                                   | 释放对象句柄资源 | obj: 要释放的对象指针                                                     | void                                | 内核空间 |
| `static TEE_Result trusty_obj_set_busy(struct trusty_tee_obj *obj)`                                         | 设置对象忙状态   | obj: 目标对象指针                                                         | TEE_SUCCESS/TEE_ERROR_BUSY          | 内核空间 |
| `static void trusty_obj_clear_busy(struct trusty_tee_obj *obj)`                                             | 清除对象忙状态   | obj: 目标对象指针                                                         | void                                | 内核空间 |

#### 4.3.2 持久对象管理辅助函数


| 函数签名                                                                                                                                                                                             | 功能描述             | 参数说明                                                                                                                                                | 返回值                  | 空间标注 |
| ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | -------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------- | ----------------------- | -------- |
| `static TEE_Result trusty_pobj_create(const struct uuid *uuid, void *obj_id, uint32_t obj_id_len, uint32_t flags, bool temporary, const struct trusty_storage_ops *fops, struct trusty_pobj **pobj)` | 创建持久对象         | uuid: TA UUID<br/>obj_id: 对象ID<br/>obj_id_len: ID长度<br/>flags: 访问标志<br/>temporary: 临时标志<br/>fops: 存储操作接口<br/>pobj: 返回的持久对象指针 | TEE_SUCCESS/TEE_ERROR_* | 内核空间 |
| `static void trusty_pobj_get(struct trusty_pobj *pobj)`                                                                                                                                              | 增加持久对象引用计数 | pobj: 持久对象指针                                                                                                                                      | void                    | 内核空间 |
| `static void trusty_pobj_put(struct trusty_pobj *pobj)`                                                                                                                                              | 减少持久对象引用计数 | pobj: 持久对象指针                                                                                                                                      | void                    | 内核空间 |
| `static struct trusty_pobj *trusty_pobj_find(const struct uuid *uuid, const void *obj_id, uint32_t obj_id_len)`                                                                                      | 查找持久对象         | uuid: TA UUID<br/>obj_id: 对象ID<br/>obj_id_len: ID长度                                                                                                 | 持久对象指针或NULL      | 内核空间 |

#### 4.3.3 存储操作辅助函数


| 函数签名                                                                                                                                                                                                                    | 功能描述     | 参数说明                                                                                                                                                                 | 返回值                  | 空间标注 |
| --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ------------ | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------ | ----------------------- | -------- |
| `static TEE_Result trusty_storage_obj_open(uint32_t storage_id, const void *obj_id, size_t obj_id_len, uint32_t flags, struct trusty_tee_obj **obj)`                                                                        | 打开持久对象 | storage_id: 存储空间ID<br/>obj_id: 对象ID<br/>obj_id_len: ID长度<br/>flags: 访问标志<br/>obj: 返回的对象句柄                                                             | TEE_SUCCESS/TEE_ERROR_* | 内核空间 |
| `static TEE_Result trusty_storage_obj_create(uint32_t storage_id, const void *obj_id, size_t obj_id_len, uint32_t flags, const struct trusty_tee_obj *attr_obj, const void *data, size_t len, struct trusty_tee_obj **obj)` | 创建持久对象 | storage_id: 存储空间ID<br/>obj_id: 对象ID<br/>obj_id_len: ID长度<br/>flags: 访问标志<br/>attr_obj: 属性对象<br/>data: 初始数据<br/>len: 数据长度<br/>obj: 返回的对象句柄 | TEE_SUCCESS/TEE_ERROR_* | 内核空间 |
| `static TEE_Result trusty_storage_obj_read(struct trusty_tee_obj *obj, void *data, size_t len, size_t *count)`                                                                                                              | 读取对象数据 | obj: 对象句柄<br/>data: 数据缓冲区<br/>len: 读取长度<br/>count: 实际读取长度                                                                                             | TEE_SUCCESS/TEE_ERROR_* | 内核空间 |
| `static TEE_Result trusty_storage_obj_write(struct trusty_tee_obj *obj, const void *data, size_t len)`                                                                                                                      | 写入对象数据 | obj: 对象句柄<br/>data: 数据缓冲区<br/>len: 写入长度                                                                                                                     | TEE_SUCCESS/TEE_ERROR_* | 内核空间 |

#### 4.3.4 属性管理辅助函数


| 函数签名                                                                                                                                         | 功能描述             | 参数说明                                                                                    | 返回值                  | 空间标注 |
| ------------------------------------------------------------------------------------------------------------------------------------------------ | -------------------- | ------------------------------------------------------------------------------------------- | ----------------------- | -------- |
| `static TEE_Result trusty_attr_pack(const TEE_Attribute *attrs, uint32_t attr_count, struct trusty_attr_transfer **packed, size_t *packed_size)` | 压缩属性数据         | attrs: 属性数组<br/>attr_count: 属性数量<br/>packed: 压缩后数据<br/>packed_size: 压缩后大小 | TEE_SUCCESS/TEE_ERROR_* | 内核空间 |
| `static TEE_Result trusty_attr_unpack(const struct trusty_attr_transfer *packed, TEE_Attribute **attrs, uint32_t *attr_count)`                   | 解压属性数据         | packed: 压缩数据<br/>attrs: 解压后属性数组<br/>attr_count: 属性数量                         | TEE_SUCCESS/TEE_ERROR_* | 内核空间 |
| `static void trusty_attr_free(TEE_Attribute *attrs, uint32_t attr_count)`                                                                        | 释放属性数据         | attrs: 属性数组<br/>attr_count: 属性数量                                                    | void                    | 内核空间 |
| `static bool trusty_attr_is_value(uint32_t attr_id)`                                                                                             | 判断属性是否为值类型 | attr_id: 属性ID                                                                             | true/false              | 内核空间 |

### 4.4 TIPC消息接口规范（基于架构图的完整映射）

#### 4.4.1 分类消息格式规范

**瞬态对象操作消息格式：**

```c
/* 瞬态对象操作请求消息 */
union transient_object_request {
    struct {
        struct gp_storage_msg_hdr hdr;
        uint32_t object_type;
        uint32_t max_object_size;
    } alloc_transient;

    struct {
        struct gp_storage_msg_hdr hdr;
        uint32_t handle_id;
    } free_transient;

    struct {
        struct gp_storage_msg_hdr hdr;
        uint32_t handle_id;
    } reset_transient;

    struct {
        struct gp_storage_msg_hdr hdr;
        uint32_t handle_id;
        uint32_t attr_count;
        uint8_t attr_data[];           // 压缩的属性数据
    } populate_transient;

    struct {
        struct gp_storage_msg_hdr hdr;
        uint32_t dest_handle_id;
        uint32_t src_handle_id;
    } copy_attributes;

    struct {
        struct gp_storage_msg_hdr hdr;
        uint32_t handle_id;
        uint32_t key_size;
        uint32_t param_count;
        uint8_t param_data[];          // 密钥生成参数
    } generate_key;
};
```

**持久对象操作消息格式：**

```c
/* 持久对象操作请求消息 */
union persistent_object_request {
    struct {
        struct gp_storage_msg_hdr hdr;
        uint32_t storage_id;
        uint32_t flags;
        size_t object_id_len;
        uint8_t object_id[];
    } open_persistent;

    struct {
        struct gp_storage_msg_hdr hdr;
        uint32_t storage_id;
        uint32_t flags;
        size_t object_id_len;
        size_t initial_data_len;
        uint32_t attr_count;
        uint8_t variable_data[];       // object_id + initial_data + attributes
    } create_persistent;

    struct {
        struct gp_storage_msg_hdr hdr;
        uint32_t handle_id;
    } close_delete_persistent;

    struct {
        struct gp_storage_msg_hdr hdr;
        uint32_t handle_id;
        size_t new_object_id_len;
        uint8_t new_object_id[];
    } rename_persistent;
};
```

**对象信息和属性操作消息格式：**

```c
/* 对象信息和属性操作请求消息 */
union info_attribute_request {
    struct {
        struct gp_storage_msg_hdr hdr;
        uint32_t handle_id;
    } get_object_info;

    struct {
        struct gp_storage_msg_hdr hdr;
        uint32_t handle_id;
        uint32_t object_usage;
    } restrict_usage;

    struct {
        struct gp_storage_msg_hdr hdr;
        uint32_t handle_id;
        uint32_t attribute_id;
        size_t buffer_size;
    } get_buffer_attr;

    struct {
        struct gp_storage_msg_hdr hdr;
        uint32_t handle_id;
        uint32_t attribute_id;
    } get_value_attr;

    struct {
        struct gp_storage_msg_hdr hdr;
        uint32_t handle_id;
    } close_object;
};
```

**数据流操作消息格式：**

```c
/* 数据流操作请求消息 */
union data_stream_request {
    struct {
        struct gp_storage_msg_hdr hdr;
        uint32_t handle_id;
        size_t read_size;
        size_t read_offset;
    } read_data;

    struct {
        struct gp_storage_msg_hdr hdr;
        uint32_t handle_id;
        size_t write_size;
        size_t write_offset;
        uint8_t write_data[];
    } write_data;

    struct {
        struct gp_storage_msg_hdr hdr;
        uint32_t handle_id;
        size_t new_size;
    } truncate_data;

    struct {
        struct gp_storage_msg_hdr hdr;
        uint32_t handle_id;
        int32_t offset;
        uint32_t whence;
    } seek_data;
};
```

**对象枚举操作消息格式：**

```c
/* 对象枚举操作请求消息 */
union object_enumeration_request {
    struct {
        struct gp_storage_msg_hdr hdr;
        uint32_t storage_id;
    } alloc_enumerator;

    struct {
        struct gp_storage_msg_hdr hdr;
        uint32_t enum_handle_id;
    } free_enumerator;

    struct {
        struct gp_storage_msg_hdr hdr;
        uint32_t enum_handle_id;
    } reset_enumerator;

    struct {
        struct gp_storage_msg_hdr hdr;
        uint32_t enum_handle_id;
        uint32_t storage_id;
    } start_enum;

    struct {
        struct gp_storage_msg_hdr hdr;
        uint32_t enum_handle_id;
    } get_next_object;
};
```

#### 4.4.2 分类响应消息格式

**统一响应消息结构：**

```c
struct gp_storage_response {
    int32_t result;                    // TEE_Result错误码
    uint32_t response_type;            // 响应类型标识
    size_t data_size;                  // 响应数据大小
    uint32_t handle_id;                // 返回的句柄ID
    union {
        /* 瞬态对象操作响应 */
        struct {
            uint32_t allocated_handle_id;
            TEE_ObjectInfo object_info;
        } transient_response;

        /* 持久对象操作响应 */
        struct {
            uint32_t persistent_handle_id;
            TEE_ObjectInfo object_info;
        } persistent_response;

        /* 对象信息和属性响应 */
        struct {
            TEE_ObjectInfo object_info;
            uint32_t attribute_value_a;
            uint32_t attribute_value_b;
            size_t buffer_size;
            uint8_t buffer_data[];
        } info_attr_response;

        /* 数据流操作响应 */
        struct {
            size_t actual_size;        // 实际读写大小
            size_t new_position;       // 新的数据位置
            uint8_t read_data[];       // 读取的数据
        } data_stream_response;

        /* 对象枚举响应 */
        struct {
            uint32_t enum_handle_id;
            TEE_ObjectInfo next_object_info;
            size_t object_id_len;
            uint8_t object_id[];
        } enumeration_response;
    };
};
```

#### 4.4.3 消息大小和性能限制

- **最大单次消息大小**：64KB（受TIPC限制）
- **大数据传输**：采用分片传输机制
- **消息队列深度**：每个TA最多16个待处理消息
- **超时处理**：30秒消息处理超时
- **分类消息优化**：根据操作类型优化消息结构大小

## 5. 关键流程设计

### 5.1 基于架构图的完整TIPC接口映射流程

#### 5.1.1 瞬态对象操作流程（完整TIPC映射）

```mermaid
sequenceDiagram
    participant TA as TA Application
    participant API as GP Storage API
    participant UTEE as UTEE Syscalls
    participant TIPC as TIPC Channel
    participant GTS as Generic TA Service
    participant OBJ as Object Management
    participant KERNEL as Kernel Syscalls

    Note over TA,KERNEL: TEE_AllocateTransientObject完整流程
    TA->>API: TEE_AllocateTransientObject()
    API->>UTEE: _utee_cryp_obj_alloc()
    UTEE->>TIPC: GP_STORAGE_ALLOC_TRANSIENT
    TIPC->>GTS: handle_alloc_transient()
    GTS->>KERNEL: syscall_cryp_obj_alloc()
    KERNEL->>OBJ: tee_obj_alloc()
    OBJ-->>KERNEL: trusty_tee_obj
    KERNEL-->>GTS: object_handle_id
    GTS->>TIPC: 发送成功响应
    TIPC->>UTEE: 响应消息
    UTEE->>API: object_handle
    API-->>TA: TEE_SUCCESS + object

    Note over TA,KERNEL: TEE_PopulateTransientObject完整流程
    TA->>API: TEE_PopulateTransientObject()
    API->>UTEE: _utee_cryp_obj_populate()
    UTEE->>TIPC: GP_STORAGE_POPULATE_TRANSIENT
    TIPC->>GTS: handle_populate_transient()
    GTS->>KERNEL: syscall_cryp_obj_populate()
    KERNEL->>OBJ: tee_obj_attr_copy_from()
    OBJ-->>KERNEL: populate_result
    KERNEL-->>GTS: TEE_SUCCESS
    GTS->>TIPC: 发送成功响应
    TIPC->>UTEE: 响应消息
    UTEE->>API: result
    API-->>TA: TEE_SUCCESS
```

#### 5.1.2 持久对象操作流程（完整TIPC映射）

```mermaid
sequenceDiagram
    participant TA as TA Application
    participant API as GP Storage API
    participant UTEE as UTEE Syscalls
    participant TIPC as TIPC Channel
    participant GTS as Generic TA Service
    participant OBJ as Object Management
    participant KERNEL as Kernel Syscalls
    participant STOR as Storage Backend

    Note over TA,STOR: TEE_CreatePersistentObject完整流程
    TA->>API: TEE_CreatePersistentObject()
    API->>UTEE: _utee_storage_obj_create()
    UTEE->>TIPC: GP_STORAGE_CREATE_PERSISTENT
    TIPC->>GTS: handle_create_persistent()
    GTS->>KERNEL: syscall_storage_obj_create()
    KERNEL->>OBJ: tee_svc_storage_init_file()
    OBJ->>STOR: create() 操作
    STOR-->>OBJ: file_handle
    OBJ-->>KERNEL: persistent_object
    KERNEL-->>GTS: object_handle_id
    GTS->>TIPC: 发送成功响应
    TIPC->>UTEE: 响应消息
    UTEE->>API: object_handle
    API-->>TA: TEE_SUCCESS + object

    Note over TA,STOR: TEE_OpenPersistentObject完整流程
    TA->>API: TEE_OpenPersistentObject()
    API->>UTEE: _utee_storage_obj_open()
    UTEE->>TIPC: GP_STORAGE_OPEN_PERSISTENT
    TIPC->>GTS: handle_open_persistent()
    GTS->>KERNEL: syscall_storage_obj_open()
    KERNEL->>OBJ: tee_pobj_get()
    OBJ->>STOR: open() 操作
    STOR-->>OBJ: file_handle
    OBJ-->>KERNEL: existing_object
    KERNEL-->>GTS: object_handle_id
    GTS->>TIPC: 发送成功响应
    TIPC->>UTEE: 响应消息
    UTEE->>API: object_handle
    API-->>TA: TEE_SUCCESS + object
```

#### 5.1.3 数据流操作流程（完整TIPC映射）

```mermaid
sequenceDiagram
    participant TA as TA Application
    participant API as GP Storage API
    participant UTEE as UTEE Syscalls
    participant TIPC as TIPC Channel
    participant GTS as Generic TA Service
    participant OBJ as Object Management
    participant KERNEL as Kernel Syscalls
    participant STOR as Storage Backend

    Note over TA,STOR: TEE_WriteObjectData完整流程
    TA->>API: TEE_WriteObjectData()
    API->>UTEE: _utee_storage_obj_write()
    UTEE->>TIPC: GP_STORAGE_WRITE_DATA
    TIPC->>GTS: handle_write_data()
    GTS->>KERNEL: syscall_storage_obj_write()
    KERNEL->>OBJ: tee_obj_get() + access_control_check()
    OBJ->>STOR: write() 操作
    STOR-->>OBJ: write_result
    OBJ->>OBJ: data_size_update()
    OBJ-->>KERNEL: write_success
    KERNEL-->>GTS: bytes_written
    GTS->>TIPC: 发送成功响应
    TIPC->>UTEE: 响应消息
    UTEE->>API: result
    API-->>TA: TEE_SUCCESS

    Note over TA,STOR: TEE_ReadObjectData完整流程
    TA->>API: TEE_ReadObjectData()
    API->>UTEE: _utee_storage_obj_read()
    UTEE->>TIPC: GP_STORAGE_READ_DATA
    TIPC->>GTS: handle_read_data()
    GTS->>KERNEL: syscall_storage_obj_read()
    KERNEL->>OBJ: tee_obj_get() + position_calculation()
    OBJ->>STOR: read() 操作
    STOR-->>OBJ: read_data
    OBJ-->>KERNEL: read_result
    KERNEL-->>GTS: data + bytes_read
    GTS->>TIPC: 发送数据响应
    TIPC->>UTEE: 响应消息 + 数据
    UTEE->>API: data + count
    API-->>TA: TEE_SUCCESS + data
```

#### 5.1.4 对象信息和属性操作流程（完整TIPC映射）

```mermaid
sequenceDiagram
    participant TA as TA Application
    participant API as GP Storage API
    participant UTEE as UTEE Syscalls
    participant TIPC as TIPC Channel
    participant GTS as Generic TA Service
    participant OBJ as Object Management
    participant KERNEL as Kernel Syscalls

    Note over TA,KERNEL: TEE_GetObjectInfo1完整流程
    TA->>API: TEE_GetObjectInfo1()
    API->>UTEE: _utee_cryp_obj_get_info()
    UTEE->>TIPC: GP_STORAGE_GET_OBJECT_INFO
    TIPC->>GTS: handle_get_object_info()
    GTS->>KERNEL: syscall_cryp_obj_get_info()
    KERNEL->>OBJ: tee_obj_get_info()
    OBJ-->>KERNEL: object_info
    KERNEL-->>GTS: TEE_ObjectInfo
    GTS->>TIPC: 发送信息响应
    TIPC->>UTEE: 响应消息
    UTEE->>API: object_info
    API-->>TA: TEE_SUCCESS + info

    Note over TA,KERNEL: TEE_GetObjectBufferAttribute完整流程
    TA->>API: TEE_GetObjectBufferAttribute()
    API->>UTEE: _utee_cryp_obj_get_attr()
    UTEE->>TIPC: GP_STORAGE_GET_BUFFER_ATTR
    TIPC->>GTS: handle_get_buffer_attr()
    GTS->>KERNEL: syscall_cryp_obj_get_attr()
    KERNEL->>OBJ: tee_obj_get_attr()
    OBJ-->>KERNEL: attribute_data
    KERNEL-->>GTS: buffer + size
    GTS->>TIPC: 发送属性响应
    TIPC->>UTEE: 响应消息 + 数据
    UTEE->>API: buffer + size
    API-->>TA: TEE_SUCCESS + attribute
```

### 5.2 基于OP-TEE busy标志的并发控制流程

```mermaid
sequenceDiagram
    participant TA1 as TA Application 1
    participant TA2 as TA Application 2
    participant API as GP Storage API
    participant UTEE as UTEE Syscalls
    participant TIPC as TIPC Channel
    participant GTS as Generic TA Service
    participant OBJ as Object Management

    Note over TA1,OBJ: 并发访问控制 - OP-TEE busy逻辑
    TA1->>API: TEE_WriteObjectData(obj)
    API->>UTEE: _utee_storage_obj_write()
    UTEE->>TIPC: GP_STORAGE_WRITE_DATA
    TIPC->>GTS: handle_write_data()
    GTS->>OBJ: trusty_obj_set_busy(obj)
    Note over OBJ: obj->busy = true
    OBJ-->>GTS: TEE_SUCCESS

    par 并发访问尝试
        TA2->>API: TEE_ReadObjectData(obj)
        API->>UTEE: _utee_storage_obj_read()
        UTEE->>TIPC: GP_STORAGE_READ_DATA
        TIPC->>GTS: handle_read_data()
        GTS->>OBJ: trusty_obj_set_busy(obj)
        Note over OBJ: obj->busy == true
        OBJ-->>GTS: TEE_ERROR_BUSY
        GTS->>TIPC: 发送忙错误响应
        TIPC->>UTEE: 错误消息
        UTEE->>API: TEE_ERROR_BUSY
        API-->>TA2: TEE_ERROR_BUSY
    and 继续第一个操作
        GTS->>OBJ: 执行写入操作
        OBJ-->>GTS: write_success
        GTS->>OBJ: trusty_obj_clear_busy(obj)
        Note over OBJ: obj->busy = false
        GTS->>TIPC: 发送成功响应
        TIPC->>UTEE: 响应消息
        UTEE->>API: result
        API-->>TA1: TEE_SUCCESS
    end
```

#### 5.2.1 对象枚举操作流程（完整TIPC映射）

```mermaid
sequenceDiagram
    participant TA as TA Application
    participant API as GP Storage API
    participant UTEE as UTEE Syscalls
    participant TIPC as TIPC Channel
    participant GTS as Generic TA Service
    participant OBJ as Object Management
    participant KERNEL as Kernel Syscalls
    participant STOR as Storage Backend

    Note over TA,STOR: TEE_AllocatePersistentObjectEnumerator完整流程
    TA->>API: TEE_AllocatePersistentObjectEnumerator()
    API->>UTEE: _utee_storage_alloc_enum()
    UTEE->>TIPC: GP_STORAGE_ALLOC_ENUMERATOR
    TIPC->>GTS: handle_alloc_enumerator()
    GTS->>KERNEL: syscall_storage_alloc_enum()
    KERNEL->>OBJ: tee_obj_enum_alloc()
    OBJ-->>KERNEL: enum_handle
    KERNEL-->>GTS: enum_handle_id
    GTS->>TIPC: 发送成功响应
    TIPC->>UTEE: 响应消息
    UTEE->>API: enum_handle
    API-->>TA: TEE_SUCCESS + enumerator

    Note over TA,STOR: TEE_StartPersistentObjectEnumerator完整流程
    TA->>API: TEE_StartPersistentObjectEnumerator()
    API->>UTEE: _utee_storage_start_enum()
    UTEE->>TIPC: GP_STORAGE_START_ENUM
    TIPC->>GTS: handle_start_enum()
    GTS->>KERNEL: syscall_storage_start_enum()
    KERNEL->>OBJ: tee_obj_enum_start()
    OBJ->>STOR: opendir() 操作
    STOR-->>OBJ: dir_handle
    OBJ-->>KERNEL: enum_started
    KERNEL-->>GTS: TEE_SUCCESS
    GTS->>TIPC: 发送成功响应
    TIPC->>UTEE: 响应消息
    UTEE->>API: result
    API-->>TA: TEE_SUCCESS

    Note over TA,STOR: TEE_GetNextPersistentObject完整流程
    TA->>API: TEE_GetNextPersistentObject()
    API->>UTEE: _utee_storage_next_enum()
    UTEE->>TIPC: GP_STORAGE_GET_NEXT_OBJECT
    TIPC->>GTS: handle_get_next_object()
    GTS->>KERNEL: syscall_storage_next_enum()
    KERNEL->>OBJ: tee_obj_enum_next()
    OBJ->>STOR: readdir() 操作
    STOR-->>OBJ: next_object_info
    OBJ-->>KERNEL: object_info + object_id
    KERNEL-->>GTS: next_object_data
    GTS->>TIPC: 发送对象信息响应
    TIPC->>UTEE: 响应消息 + 对象数据
    UTEE->>API: object_info + object_id
    API-->>TA: TEE_SUCCESS + next_object
```

### 5.3 基于OP-TEE引用计数的对象共享流程（完整TIPC映射）

```mermaid
sequenceDiagram
    participant TA1 as TA Application 1
    participant TA2 as TA Application 2
    participant API as GP Storage API
    participant UTEE as UTEE Syscalls
    participant TIPC as TIPC Channel
    participant GTS as Generic TA Service
    participant OBJ as Object Management
    participant POBJ as Persistent Object Manager

    Note over TA1,POBJ: TA1创建持久对象 - 完整TIPC流程
    TA1->>API: TEE_CreatePersistentObject("shared_obj")
    API->>UTEE: _utee_storage_obj_create()
    UTEE->>TIPC: GP_STORAGE_CREATE_PERSISTENT
    TIPC->>GTS: handle_create_persistent()
    GTS->>POBJ: trusty_pobj_create()
    Note over POBJ: pobj->refcnt = 1<br/>pobj->uuid = TA1_UUID
    POBJ-->>GTS: trusty_pobj
    GTS->>OBJ: trusty_obj_alloc() for TA1
    Note over OBJ: obj1->pobj = pobj
    OBJ-->>GTS: trusty_tee_obj (obj1)
    GTS->>TIPC: 发送成功响应
    TIPC->>UTEE: 响应消息
    UTEE->>API: object_handle
    API-->>TA1: TEE_SUCCESS + handle1

    Note over TA1,POBJ: TA1再次打开同一对象 - 完整TIPC流程
    TA1->>API: TEE_OpenPersistentObject("shared_obj")
    API->>UTEE: _utee_storage_obj_open()
    UTEE->>TIPC: GP_STORAGE_OPEN_PERSISTENT
    TIPC->>GTS: handle_open_persistent()
    GTS->>POBJ: trusty_pobj_find() 找到现有pobj
    POBJ-->>GTS: existing_pobj
    GTS->>POBJ: trusty_pobj_get() 增加引用计数
    Note over POBJ: pobj->refcnt = 2<br/>基于OP-TEE引用计数逻辑
    GTS->>OBJ: trusty_obj_alloc() for TA1
    Note over OBJ: obj2->pobj = same_pobj
    OBJ-->>GTS: trusty_tee_obj (obj2)
    GTS->>TIPC: 发送成功响应
    TIPC->>UTEE: 响应消息
    UTEE->>API: object_handle
    API-->>TA1: TEE_SUCCESS + handle2

    Note over TA1,POBJ: TA1关闭第一个句柄 - 完整TIPC流程
    TA1->>API: TEE_CloseObject(handle1)
    API->>UTEE: _utee_cryp_obj_close()
    UTEE->>TIPC: GP_STORAGE_CLOSE_OBJECT
    TIPC->>GTS: handle_close_object()
    GTS->>OBJ: trusty_obj_free(obj1)
    OBJ->>POBJ: trusty_pobj_put() 减少引用计数
    Note over POBJ: pobj->refcnt = 1<br/>对象仍然存在
    POBJ-->>OBJ: 对象未释放
    OBJ-->>GTS: 句柄已释放
    GTS->>TIPC: 发送成功响应
    TIPC->>UTEE: 响应消息
    UTEE->>API: result
    API-->>TA1: TEE_SUCCESS
```

## 6. 系统错误处理设计

### 6.1 基于OP-TEE的错误处理策略

#### 6.1.1 分层错误处理机制

**API层错误处理（基于OP-TEE验证逻辑）：**

- 参数验证和类型检查：完全复制OP-TEE的参数验证逻辑
- 句柄有效性验证：使用OP-TEE的句柄验证机制
- 权限检查：基于OP-TEE的访问控制策略

**TIPC层错误处理：**

- 消息格式验证：确保与OP-TEE消息格式兼容
- 通信超时处理：30秒超时机制
- 连接异常恢复：自动重连和状态恢复

**服务层错误处理（基于OP-TEE逻辑）：**

- 资源不足处理：基于OP-TEE的资源管理策略
- 并发冲突解决：使用OP-TEE的busy标志和creating标志机制
- 存储异常处理：基于OP-TEE的存储错误处理逻辑

**存储层错误处理：**

- 文件系统错误映射：Trusty存储错误到GP错误码的映射
- 存储空间不足处理：基于OP-TEE的空间管理策略
- 数据完整性检查：使用OP-TEE的完整性验证机制

#### 6.1.2 OP-TEE兼容的错误码映射

**完整错误码映射表：**


| GP错误码                        | Trusty错误码       | OP-TEE对应错误                  | 处理策略         |
| ------------------------------- | ------------------ | ------------------------------- | ---------------- |
| TEE_ERROR_OUT_OF_MEMORY         | ERR_NO_MEMORY      | TEE_ERROR_OUT_OF_MEMORY         | 资源清理后重试   |
| TEE_ERROR_STORAGE_NOT_AVAILABLE | ERR_NOT_READY      | TEE_ERROR_STORAGE_NOT_AVAILABLE | 等待存储服务恢复 |
| TEE_ERROR_ACCESS_CONFLICT       | ERR_ALREADY_EXISTS | TEE_ERROR_ACCESS_CONFLICT       | 返回冲突信息     |
| TEE_ERROR_CORRUPT_OBJECT        | ERR_GENERIC        | TEE_ERROR_CORRUPT_OBJECT        | 删除损坏对象     |
| TEE_ERROR_BUSY                  | ERR_BUSY           | TEE_ERROR_BUSY                  | 等待对象空闲     |
| TEE_ERROR_ITEM_NOT_FOUND        | ERR_NOT_FOUND      | TEE_ERROR_ITEM_NOT_FOUND        | 返回未找到状态   |
| TEE_ERROR_BAD_PARAMETERS        | ERR_INVALID_ARGS   | TEE_ERROR_BAD_PARAMETERS        | 参数验证失败     |

#### 6.1.3 异常恢复机制

**基于OP-TEE的资源清理策略：**

```c
/* TA断开连接时的资源清理 - 基于OP-TEE逻辑 */
static void trusty_storage_cleanup_ta_context(struct uuid *ta_uuid) {
    struct trusty_storage_context *ctx;
    struct trusty_tee_obj *obj, *tmp_obj;
    struct trusty_pobj *pobj, *tmp_pobj;

    /* 清理TA的所有对象句柄 */
    list_for_every_entry_safe(&ctx->transient_objects, obj, tmp_obj,
                             struct trusty_tee_obj, link) {
        /* 基于OP-TEE的对象清理逻辑 */
        if (obj->busy) {
            trusty_obj_clear_busy(obj);  /* 清除忙标志 */
        }
        if (obj->pobj) {
            trusty_pobj_put(obj->pobj);  /* 减少引用计数 */
        }
        trusty_obj_free(obj);            /* 释放对象句柄 */
    }

    /* 清理持久对象句柄 */
    list_for_every_entry_safe(&ctx->persistent_handles, obj, tmp_obj,
                             struct trusty_tee_obj, link) {
        if (obj->pobj) {
            trusty_pobj_put(obj->pobj);  /* 基于OP-TEE引用计数逻辑 */
        }
        trusty_obj_free(obj);
    }
}
```

**存储事务原子性保证：**

- 基于OP-TEE的creating标志确保创建操作的原子性
- 使用OP-TEE的temporary标志管理临时对象状态
- 文件操作失败时的回滚机制

### 6.2 并发访问控制机制

#### 6.2.1 基于OP-TEE的锁机制设计

**多级锁策略（完全基于OP-TEE设计）：**

1. **全局持久对象锁**：保护全局持久对象链表
2. **对象句柄锁**：保护单个对象的并发访问（基于OP-TEE busy标志）
3. **存储上下文锁**：保护TA级别的存储状态
4. **文件级锁**：保护底层存储文件的读写

**OP-TEE并发控制适配实现：**

```c
/* 基于OP-TEE的对象访问控制 */
static TEE_Result trusty_obj_check_access_conflict(struct trusty_pobj *pobj,
                                                  uint32_t flags) {
    /* 基于OP-TEE的访问冲突检测逻辑 */
    if (pobj->creating) {
        return TEE_ERROR_BUSY;  /* 对象创建中，拒绝访问 */
    }

    /* 检查访问标志冲突 */
    if ((flags & TEE_DATA_FLAG_ACCESS_WRITE) &&
        (pobj->flags & TEE_DATA_FLAG_SHARE_WRITE) == 0) {
        return TEE_ERROR_ACCESS_CONFLICT;
    }

    return TEE_SUCCESS;
}
```

#### 6.2.2 共享访问控制

**基于OP-TEE的访问权限矩阵：**


| 操作类型   | 读权限 | 写权限 | 元数据写权限 | OP-TEE对应检查                  |
| ---------- | ------ | ------ | ------------ | ------------------------------- |
| 读取数据   | 必需   | -      | -            | TEE_DATA_FLAG_ACCESS_READ       |
| 写入数据   | -      | 必需   | -            | TEE_DATA_FLAG_ACCESS_WRITE      |
| 删除对象   | -      | -      | 必需         | TEE_DATA_FLAG_ACCESS_WRITE_META |
| 重命名对象 | -      | -      | 必需         | TEE_DATA_FLAG_ACCESS_WRITE_META |

**基于OP-TEE的冲突检测规则：**

- 同一对象的多个读句柄可以并存（基于OP-TEE共享读逻辑）
- 写句柄与任何其他句柄互斥（基于OP-TEE独占写逻辑）
- 元数据写权限完全独占（基于OP-TEE元数据保护逻辑）

### 6.3 错误恢复和状态一致性

#### 6.3.1 基于OP-TEE的状态一致性保证

**对象状态一致性机制：**

```c
/* 基于OP-TEE的对象状态恢复 */
static TEE_Result trusty_obj_recover_state(struct trusty_tee_obj *obj) {
    /* 检查对象状态一致性 */
    if (obj->pobj && obj->pobj->creating) {
        /* 如果持久对象仍在创建中，说明之前的创建操作被中断 */
        obj->pobj->creating = false;
        obj->pobj->temporary = false;
    }

    /* 重置busy标志 */
    if (obj->busy) {
        obj->busy = false;
    }

    /* 重置数据流位置 */
    obj->ds_pos = 0;

    return TEE_SUCCESS;
}
```

#### 6.3.2 存储一致性和完整性检查

**基于OP-TEE的完整性验证：**

- 对象ID和存储路径的一致性检查
- 属性数据的完整性验证
- 文件句柄和持久对象的关联性检查
- 引用计数的一致性验证

**数据恢复策略：**

- 损坏对象的自动清理和重建
- 孤立文件的检测和清理
- 不一致状态的自动修复

## 7. 系统维护设计

### 7.1 基于OP-TEE的性能优化策略

#### 7.1.1 内存管理优化

**基于OP-TEE的对象池化管理：**

```c
/* 对象句柄池 - 基于OP-TEE内存管理策略 */
struct trusty_obj_pool {
    struct trusty_tee_obj *free_objects[MAX_POOLED_OBJECTS];
    uint32_t free_count;
    uint32_t total_allocated;
    uint32_t peak_usage;
    mutex_t pool_lock;
};

/* 基于OP-TEE的对象分配优化 */
static struct trusty_tee_obj *trusty_obj_pool_alloc(void) {
    struct trusty_tee_obj *obj = NULL;

    mutex_acquire(&obj_pool.pool_lock);
    if (obj_pool.free_count > 0) {
        obj = obj_pool.free_objects[--obj_pool.free_count];
        /* 重置对象状态 - 基于OP-TEE重置逻辑 */
        memset(&obj->info, 0, sizeof(obj->info));
        obj->busy = false;
        obj->have_attrs = 0;
        obj->attr = NULL;
        obj->ds_pos = 0;
        obj->pobj = NULL;
        obj->fh = NULL;
    }
    mutex_release(&obj_pool.pool_lock);

    if (!obj) {
        obj = calloc(1, sizeof(*obj));
        if (obj) {
            mutex_init(&obj->obj_lock);
        }
    }

    return obj;
}
```

**属性数据缓存机制：**

- 基于OP-TEE的have_attrs位字段优化属性存储
- 瞬态对象属性缓存，避免重复序列化
- 属性数据的延迟加载和按需分配

### 7.2 监控和诊断机制

#### 7.2.1 性能监控

**基于OP-TEE的统计信息收集：**

```c
/* 存储性能统计 - 基于OP-TEE监控机制 */
struct trusty_storage_stats {
    /* 对象操作统计 */
    uint64_t transient_alloc_count;    // 瞬态对象分配次数
    uint64_t persistent_open_count;    // 持久对象打开次数
    uint64_t persistent_create_count;  // 持久对象创建次数

    /* 数据操作统计 */
    uint64_t read_operations;          // 读操作次数
    uint64_t write_operations;         // 写操作次数
    uint64_t bytes_read;               // 总读取字节数
    uint64_t bytes_written;            // 总写入字节数

    /* 并发控制统计 */
    uint64_t busy_conflicts;           // busy冲突次数
    uint64_t access_conflicts;         // 访问冲突次数
    uint64_t creating_conflicts;       // 创建冲突次数

    /* 性能指标 */
    uint32_t avg_response_time_us;     // 平均响应时间（微秒）
    uint32_t peak_concurrent_objects;  // 峰值并发对象数
    uint32_t current_memory_usage;     // 当前内存使用量
};
```

#### 7.2.2 错误诊断和日志

**基于OP-TEE的诊断信息：**

- 对象状态跟踪：busy标志、creating标志、引用计数状态
- 并发冲突分析：访问冲突、创建冲突的详细信息
- 内存泄漏检测：对象分配和释放的配对检查
- 性能瓶颈识别：慢操作和资源竞争的识别

### 7.3 配置和调优

#### 7.3.1 系统配置参数

**基于OP-TEE的可配置参数：**

```c
/* 存储系统配置 - 基于OP-TEE配置机制 */
struct trusty_storage_config {
    /* 对象限制配置 */
    uint32_t max_objects_per_ta;       // 每个TA最大对象数
    uint32_t max_concurrent_objects;   // 最大并发对象数
    uint32_t max_object_size;          // 最大对象大小

    /* 性能配置 */
    uint32_t object_pool_size;         // 对象池大小
    uint32_t attr_cache_size;          // 属性缓存大小
    uint32_t io_buffer_size;           // I/O缓冲区大小

    /* 超时配置 */
    uint32_t operation_timeout_ms;     // 操作超时时间
    uint32_t busy_wait_timeout_ms;     // busy等待超时时间

    /* 调试配置 */
    bool enable_debug_logging;         // 启用调试日志
    bool enable_performance_stats;     // 启用性能统计
    bool enable_memory_tracking;       // 启用内存跟踪
};
```

#### 7.4.2 故障排查和恢复

**基于OP-TEE的故障诊断：**

```c
/* 存储系统健康检查 - 基于OP-TEE诊断机制 */
static TEE_Result trusty_storage_health_check(void) {
    TEE_Result res = TEE_SUCCESS;

    /* 检查全局持久对象链表一致性 */
    res = check_global_pobj_consistency();
    if (res != TEE_SUCCESS) {
        EMSG("Global pobj list inconsistency detected");
        return res;
    }

    /* 检查引用计数一致性 */
    res = check_refcount_consistency();
    if (res != TEE_SUCCESS) {
        EMSG("Reference count inconsistency detected");
        return res;
    }

    /* 检查对象状态一致性 */
    res = check_object_state_consistency();
    if (res != TEE_SUCCESS) {
        EMSG("Object state inconsistency detected");
        return res;
    }

    return TEE_SUCCESS;
}
```

**自动恢复机制：**

- 损坏对象的自动清理：基于OP-TEE的对象验证逻辑
- 孤立文件的检测和清理：基于OP-TEE的存储管理机制
- 不一致状态的自动修复：基于OP-TEE的状态恢复逻辑
